package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.phad.chatapp.features.scheduling.models.SubjectPreset
import com.phad.chatapp.features.scheduling.models.ScheduleSubjectPairing
import com.phad.chatapp.features.scheduling.ui.components.StandardButton
import com.phad.chatapp.features.scheduling.ui.theme.DarkBackground
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface
import com.phad.chatapp.features.scheduling.ui.theme.YellowAccent
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.features.scheduling.firebase.FirestoreCollection
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

// Simple data class for schedule presets
data class SchedulePreset(
    val id: String = "",
    val name: String = "",
    val createdAt: Long = 0
)

@Composable
fun SubjectPresetSelectionScreen(navController: NavController) {
    var schedulePresets by remember { mutableStateOf<List<SchedulePreset>>(emptyList()) }
    var subjectPresets by remember { mutableStateOf<List<SubjectPreset>>(emptyList()) }
    var selectedSchedules by remember { mutableStateOf<Set<String>>(emptySet()) }
    var schedulePairings by remember { mutableStateOf<Map<String, String>>(emptyMap()) }
    var isLoading by remember { mutableStateOf(true) }
    var isSaving by remember { mutableStateOf(false) }
    
    val coroutineScope = rememberCoroutineScope()

    // Load data
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            val schedules = loadSchedulePresets()
            val subjects = loadSubjectPresets()
            schedulePresets = schedules
            subjectPresets = subjects
            isLoading = false
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(DarkBackground)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp, vertical = 8.dp)
        ) {
            // Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { navController.navigateUp() },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Assign Subjects to Schedules",
                    style = MaterialTheme.typography.titleLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                )
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = YellowAccent)
                }
            } else if (schedulePresets.isEmpty()) {
                // No schedules available
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            "No Schedule Presets Found",
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            "Create schedule presets first to assign subjects",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFFB0B0B0)
                        )
                    }
                }
            } else if (subjectPresets.isEmpty()) {
                // No subject presets available
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            "No Subject Presets Found",
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            "Create subject presets first to assign to schedules",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFFB0B0B0)
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        StandardButton(
                            onClick = { navController.navigateUp() }
                        ) {
                            Text(
                                "Create Subject Presets",
                                color = Color.Black,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            } else {
                // Main content
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    contentPadding = PaddingValues(vertical = 16.dp),
                    modifier = Modifier.weight(1f)
                ) {
                    // Instructions
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                Text(
                                    "Instructions",
                                    style = MaterialTheme.typography.titleSmall,
                                    color = Color.White,
                                    fontWeight = FontWeight.Medium
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    "1. Select schedule presets you want to assign subjects to\n" +
                                    "2. For each selected schedule, choose a subject preset\n" +
                                    "3. Save the assignments",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFFB0B0B0)
                                )
                            }
                        }
                    }

                    // Schedule Presets
                    items(schedulePresets) { schedule ->
                        SchedulePresetCard(
                            schedule = schedule,
                            isSelected = selectedSchedules.contains(schedule.id),
                            selectedSubjectPresetId = schedulePairings[schedule.id],
                            subjectPresets = subjectPresets,
                            onScheduleToggle = { scheduleId ->
                                selectedSchedules = if (selectedSchedules.contains(scheduleId)) {
                                    selectedSchedules - scheduleId
                                } else {
                                    selectedSchedules + scheduleId
                                }
                            },
                            onSubjectPresetSelected = { scheduleId, subjectPresetId ->
                                schedulePairings = schedulePairings + (scheduleId to subjectPresetId)
                            }
                        )
                    }
                }
                
                // Save Button
                if (selectedSchedules.isNotEmpty()) {
                    StandardButton(
                        onClick = {
                            // TODO: Implement save functionality
                            // For now, just show a message
                            coroutineScope.launch {
                                // This is where you would save the pairings
                                // For the first phase, we'll just navigate back
                                navController.navigateUp()
                            }
                        },
                        enabled = !isSaving && schedulePairings.keys.containsAll(selectedSchedules),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                    ) {
                        if (isSaving) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.Black,
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Text(
                            "Save Assignments",
                            color = Color.Black,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SchedulePresetCard(
    schedule: SchedulePreset,
    isSelected: Boolean,
    selectedSubjectPresetId: String?,
    subjectPresets: List<SubjectPreset>,
    onScheduleToggle: (String) -> Unit,
    onSubjectPresetSelected: (String, String) -> Unit
) {
    var showSubjectDropdown by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onScheduleToggle(schedule.id) },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) NeutralCardSurface else Color(0xFF0F0F0F)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = schedule.name,
                        style = MaterialTheme.typography.titleSmall,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Text(
                        text = "Schedule Preset",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFB0B0B0)
                    )
                }
                
                if (isSelected) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = YellowAccent,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
            
            // Subject preset selection (only show if schedule is selected)
            if (isSelected) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    "Select Subject Preset:",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFB0B0B0),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Box {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { showSubjectDropdown = true },
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(containerColor = DarkBackground)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = selectedSubjectPresetId?.let { id ->
                                    subjectPresets.find { it.id == id }?.name ?: "Select Subject Preset"
                                } ?: "Select Subject Preset",
                                color = if (selectedSubjectPresetId != null) Color.White else Color(0xFFB0B0B0),
                                style = MaterialTheme.typography.bodyMedium
                            )
                            
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = "Dropdown",
                                tint = Color(0xFFB0B0B0)
                            )
                        }
                    }
                    
                    DropdownMenu(
                        expanded = showSubjectDropdown,
                        onDismissRequest = { showSubjectDropdown = false },
                        modifier = Modifier.background(NeutralCardSurface)
                    ) {
                        subjectPresets.forEach { preset ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        preset.name,
                                        color = Color.White
                                    )
                                },
                                onClick = {
                                    onSubjectPresetSelected(schedule.id, preset.id)
                                    showSubjectDropdown = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

private suspend fun loadSchedulePresets(): List<SchedulePreset> {
    return try {
        val db = FirebaseFirestore.getInstance()
        val snapshot = db.collection(FirestoreCollection.GENERATED_SCHEDULES).get().await()
        snapshot.documents.mapNotNull { doc ->
            val name = doc.getString("name") ?: doc.id
            val createdAt = doc.getLong("createdAt") ?: 0
            SchedulePreset(
                id = doc.id,
                name = name,
                createdAt = createdAt
            )
        }
    } catch (e: Exception) {
        emptyList()
    }
}

private suspend fun loadSubjectPresets(): List<SubjectPreset> {
    return try {
        val db = FirebaseFirestore.getInstance()
        val snapshot = db.collection(FirestoreCollection.SUBJECT_PRESETS).get().await()
        snapshot.documents.mapNotNull { doc ->
            doc.toObject(SubjectPreset::class.java)?.copy(id = doc.id)
        }
    } catch (e: Exception) {
        emptyList()
    }
}
